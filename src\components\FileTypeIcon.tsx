import React from 'react'
import { Icon, Icons } from './icons/FontAwesomeIcons'
import { FileTreeNode } from '../types'

interface FileTypeIconProps {
  file: FileTreeNode
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl'
  showState?: boolean
  state?: 'loading' | 'error' | 'success' | 'syncing' | 'locked' | 'linked'
  className?: string
}

interface FileTypeInfo {
  icon: any
  color: string
  bgColor: string
  category: 'document' | 'code' | 'media' | 'archive' | 'data' | 'system' | 'folder'
  description: string
}

const FileTypeIcon: React.FC<FileTypeIconProps> = ({
  file,
  size = 'md',
  showState = false,
  state,
  className = ''
}) => {
  const getFileTypeInfo = (file: FileTreeNode): FileTypeInfo => {
    if (file.type === 'folder') {
      return {
        icon: file.isExpanded ? Icons.folderOpen : Icons.folder,
        color: 'text-primary',
        bgColor: 'bg-primary/10',
        category: 'folder',
        description: 'Folder'
      }
    }

    const extension = file.name.split('.').pop()?.toLowerCase() || ''
    
    // Text and document files
    if (['txt', 'rtf', 'log'].includes(extension)) {
      return {
        icon: Icons.fileText,
        color: 'text-neutral-400',
        bgColor: 'bg-neutral-400/10',
        category: 'document',
        description: 'Text Document'
      }
    }

    if (['md', 'markdown'].includes(extension)) {
      return {
        icon: Icons.fileLines,
        color: 'text-blue-400',
        bgColor: 'bg-blue-400/10',
        category: 'document',
        description: 'Markdown Document'
      }
    }

    if (['doc', 'docx'].includes(extension)) {
      return {
        icon: Icons.fileWord,
        color: 'text-blue-600',
        bgColor: 'bg-blue-600/10',
        category: 'document',
        description: 'Word Document'
      }
    }

    if (['xls', 'xlsx', 'csv'].includes(extension)) {
      return {
        icon: faFileExcel,
        color: 'text-green-600',
        bgColor: 'bg-green-600/10',
        category: 'document',
        description: 'Spreadsheet'
      }
    }

    if (['ppt', 'pptx'].includes(extension)) {
      return {
        icon: faFilePowerpoint,
        color: 'text-orange-600',
        bgColor: 'bg-orange-600/10',
        category: 'document',
        description: 'Presentation'
      }
    }

    if (extension === 'pdf') {
      return {
        icon: faFilePdf,
        color: 'text-red-500',
        bgColor: 'bg-red-500/10',
        category: 'document',
        description: 'PDF Document'
      }
    }

    // Code files
    if (['js', 'jsx', 'mjs'].includes(extension)) {
      return {
        icon: faFileCode,
        color: 'text-yellow-500',
        bgColor: 'bg-yellow-500/10',
        category: 'code',
        description: 'JavaScript'
      }
    }

    if (['ts', 'tsx'].includes(extension)) {
      return {
        icon: faFileCode,
        color: 'text-blue-500',
        bgColor: 'bg-blue-500/10',
        category: 'code',
        description: 'TypeScript'
      }
    }

    if (['py', 'pyw'].includes(extension)) {
      return {
        icon: faFileCode,
        color: 'text-green-500',
        bgColor: 'bg-green-500/10',
        category: 'code',
        description: 'Python'
      }
    }

    if (['java', 'class', 'jar'].includes(extension)) {
      return {
        icon: faFileCode,
        color: 'text-orange-500',
        bgColor: 'bg-orange-500/10',
        category: 'code',
        description: 'Java'
      }
    }

    if (['cpp', 'cc', 'cxx', 'c++'].includes(extension)) {
      return {
        icon: faFileCode,
        color: 'text-blue-400',
        bgColor: 'bg-blue-400/10',
        category: 'code',
        description: 'C++'
      }
    }

    if (['c', 'h'].includes(extension)) {
      return {
        icon: faFileCode,
        color: 'text-gray-400',
        bgColor: 'bg-gray-400/10',
        category: 'code',
        description: 'C'
      }
    }

    if (['cs'].includes(extension)) {
      return {
        icon: faFileCode,
        color: 'text-purple-500',
        bgColor: 'bg-purple-500/10',
        category: 'code',
        description: 'C#'
      }
    }

    if (['php'].includes(extension)) {
      return {
        icon: faFileCode,
        color: 'text-indigo-500',
        bgColor: 'bg-indigo-500/10',
        category: 'code',
        description: 'PHP'
      }
    }

    if (['rb', 'ruby'].includes(extension)) {
      return {
        icon: faFileCode,
        color: 'text-red-400',
        bgColor: 'bg-red-400/10',
        category: 'code',
        description: 'Ruby'
      }
    }

    if (['go'].includes(extension)) {
      return {
        icon: faFileCode,
        color: 'text-cyan-500',
        bgColor: 'bg-cyan-500/10',
        category: 'code',
        description: 'Go'
      }
    }

    if (['rs'].includes(extension)) {
      return {
        icon: faFileCode,
        color: 'text-orange-400',
        bgColor: 'bg-orange-400/10',
        category: 'code',
        description: 'Rust'
      }
    }

    if (['swift'].includes(extension)) {
      return {
        icon: faFileCode,
        color: 'text-orange-300',
        bgColor: 'bg-orange-300/10',
        category: 'code',
        description: 'Swift'
      }
    }

    if (['kt', 'kts'].includes(extension)) {
      return {
        icon: faFileCode,
        color: 'text-purple-400',
        bgColor: 'bg-purple-400/10',
        category: 'code',
        description: 'Kotlin'
      }
    }

    if (['html', 'htm'].includes(extension)) {
      return {
        icon: faFileCode,
        color: 'text-orange-500',
        bgColor: 'bg-orange-500/10',
        category: 'code',
        description: 'HTML'
      }
    }

    if (['css', 'scss', 'sass', 'less'].includes(extension)) {
      return {
        icon: faFileCode,
        color: 'text-blue-400',
        bgColor: 'bg-blue-400/10',
        category: 'code',
        description: 'Stylesheet'
      }
    }

    // Data files
    if (['json', 'jsonl'].includes(extension)) {
      return {
        icon: faDatabase,
        color: 'text-yellow-400',
        bgColor: 'bg-yellow-400/10',
        category: 'data',
        description: 'JSON Data'
      }
    }

    if (['xml', 'xsd', 'xsl'].includes(extension)) {
      return {
        icon: faDatabase,
        color: 'text-green-400',
        bgColor: 'bg-green-400/10',
        category: 'data',
        description: 'XML Data'
      }
    }

    if (['yaml', 'yml'].includes(extension)) {
      return {
        icon: faDatabase,
        color: 'text-red-400',
        bgColor: 'bg-red-400/10',
        category: 'data',
        description: 'YAML Data'
      }
    }

    if (['sql', 'db', 'sqlite', 'sqlite3'].includes(extension)) {
      return {
        icon: faDatabase,
        color: 'text-blue-300',
        bgColor: 'bg-blue-300/10',
        category: 'data',
        description: 'Database'
      }
    }

    // Media files
    if (['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg', 'ico', 'tiff', 'tif'].includes(extension)) {
      return {
        icon: faFileImage,
        color: 'text-green-400',
        bgColor: 'bg-green-400/10',
        category: 'media',
        description: 'Image'
      }
    }

    if (['mp4', 'avi', 'mov', 'wmv', 'flv', 'webm', 'mkv', 'm4v', '3gp'].includes(extension)) {
      return {
        icon: faFileVideo,
        color: 'text-purple-400',
        bgColor: 'bg-purple-400/10',
        category: 'media',
        description: 'Video'
      }
    }

    if (['mp3', 'wav', 'flac', 'aac', 'ogg', 'wma', 'm4a', 'opus'].includes(extension)) {
      return {
        icon: faFileAudio,
        color: 'text-pink-400',
        bgColor: 'bg-pink-400/10',
        category: 'media',
        description: 'Audio'
      }
    }

    // Archive files
    if (['zip', 'rar', '7z', 'tar', 'gz', 'bz2', 'xz', 'lz', 'lzma'].includes(extension)) {
      return {
        icon: faFileArchive,
        color: 'text-amber-500',
        bgColor: 'bg-amber-500/10',
        category: 'archive',
        description: 'Archive'
      }
    }

    // System and config files
    if (['ini', 'cfg', 'conf', 'config', 'toml'].includes(extension)) {
      return {
        icon: faGear,
        color: 'text-gray-400',
        bgColor: 'bg-gray-400/10',
        category: 'system',
        description: 'Configuration'
      }
    }

    if (['env', 'dotenv'].includes(extension)) {
      return {
        icon: faGear,
        color: 'text-yellow-300',
        bgColor: 'bg-yellow-300/10',
        category: 'system',
        description: 'Environment'
      }
    }

    // Default
    return {
      icon: faFile,
      color: 'text-neutral-400',
      bgColor: 'bg-neutral-400/10',
      category: 'document',
      description: 'File'
    }
  }

  const getStateIcon = (state: string) => {
    switch (state) {
      case 'loading': return faSpinner
      case 'error': return faExclamationTriangle
      case 'success': return faCheck
      case 'syncing': return faSync
      case 'locked': return faLock
      case 'linked': return faLink
      default: return null
    }
  }

  const getStateColor = (state: string) => {
    switch (state) {
      case 'loading': return 'text-blue-400'
      case 'error': return 'text-red-400'
      case 'success': return 'text-green-400'
      case 'syncing': return 'text-yellow-400'
      case 'locked': return 'text-orange-400'
      case 'linked': return 'text-purple-400'
      default: return 'text-neutral-400'
    }
  }

  const getSizeClasses = (size: string) => {
    switch (size) {
      case 'xs': return 'w-3 h-3'
      case 'sm': return 'w-4 h-4'
      case 'md': return 'w-5 h-5'
      case 'lg': return 'w-6 h-6'
      case 'xl': return 'w-8 h-8'
      default: return 'w-5 h-5'
    }
  }

  const fileTypeInfo = getFileTypeInfo(file)
  const sizeClasses = getSizeClasses(size)
  const stateIcon = state ? getStateIcon(state) : null
  const stateColor = state ? getStateColor(state) : ''

  return (
    <div className={`relative inline-flex items-center justify-center ${className}`}>
      {/* Main file icon */}
      <div className={`
        flex items-center justify-center rounded-lg p-1
        ${size === 'xl' ? 'p-2' : size === 'lg' ? 'p-1.5' : 'p-1'}
        ${fileTypeInfo.bgColor}
      `}>
        <FontAwesomeIcon 
          icon={fileTypeInfo.icon} 
          className={`${sizeClasses} ${fileTypeInfo.color}`}
        />
      </div>

      {/* State indicator */}
      {showState && state && stateIcon && (
        <div className={`
          absolute -top-1 -right-1 flex items-center justify-center
          w-4 h-4 bg-neutral-800 border border-neutral-700 rounded-full
          ${size === 'xs' ? 'w-3 h-3 -top-0.5 -right-0.5' : ''}
          ${size === 'xl' ? 'w-5 h-5 -top-1.5 -right-1.5' : ''}
        `}>
          <FontAwesomeIcon 
            icon={stateIcon} 
            className={`
              w-2 h-2 ${stateColor}
              ${state === 'loading' || state === 'syncing' ? 'animate-spin' : ''}
              ${size === 'xs' ? 'w-1.5 h-1.5' : ''}
              ${size === 'xl' ? 'w-3 h-3' : ''}
            `}
          />
        </div>
      )}

      {/* File count badge for folders */}
      {file.type === 'folder' && file.fileCount !== undefined && file.fileCount > 0 && (
        <div className={`
          absolute -bottom-1 -right-1 flex items-center justify-center
          min-w-4 h-4 px-1 bg-primary text-white text-xs rounded-full
          ${size === 'xs' ? 'min-w-3 h-3 text-xs -bottom-0.5 -right-0.5' : ''}
          ${size === 'xl' ? 'min-w-5 h-5 text-sm -bottom-1.5 -right-1.5' : ''}
        `}>
          {file.fileCount > 99 ? '99+' : file.fileCount}
        </div>
      )}
    </div>
  )
}

export default FileTypeIcon

// Export utility functions for use in other components
export const getFileCategory = (filename: string): string => {
  const extension = filename.split('.').pop()?.toLowerCase() || ''
  
  if (['txt', 'rtf', 'log', 'md', 'markdown', 'doc', 'docx', 'xls', 'xlsx', 'csv', 'ppt', 'pptx', 'pdf'].includes(extension)) {
    return 'document'
  }
  
  if (['js', 'jsx', 'mjs', 'ts', 'tsx', 'py', 'pyw', 'java', 'class', 'jar', 'cpp', 'cc', 'cxx', 'c++', 'c', 'h', 'cs', 'php', 'rb', 'ruby', 'go', 'rs', 'swift', 'kt', 'kts', 'html', 'htm', 'css', 'scss', 'sass', 'less'].includes(extension)) {
    return 'code'
  }
  
  if (['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg', 'ico', 'tiff', 'tif', 'mp4', 'avi', 'mov', 'wmv', 'flv', 'webm', 'mkv', 'm4v', '3gp', 'mp3', 'wav', 'flac', 'aac', 'ogg', 'wma', 'm4a', 'opus'].includes(extension)) {
    return 'media'
  }
  
  if (['zip', 'rar', '7z', 'tar', 'gz', 'bz2', 'xz', 'lz', 'lzma'].includes(extension)) {
    return 'archive'
  }
  
  if (['json', 'jsonl', 'xml', 'xsd', 'xsl', 'yaml', 'yml', 'sql', 'db', 'sqlite', 'sqlite3'].includes(extension)) {
    return 'data'
  }
  
  if (['ini', 'cfg', 'conf', 'config', 'toml', 'env', 'dotenv'].includes(extension)) {
    return 'system'
  }
  
  return 'document'
}

export const getFileDescription = (filename: string): string => {
  const extension = filename.split('.').pop()?.toLowerCase() || ''
  
  const descriptions: Record<string, string> = {
    // Documents
    txt: 'Text Document',
    md: 'Markdown Document',
    doc: 'Word Document',
    docx: 'Word Document',
    xls: 'Excel Spreadsheet',
    xlsx: 'Excel Spreadsheet',
    csv: 'CSV Spreadsheet',
    ppt: 'PowerPoint Presentation',
    pptx: 'PowerPoint Presentation',
    pdf: 'PDF Document',
    
    // Code
    js: 'JavaScript',
    jsx: 'React Component',
    ts: 'TypeScript',
    tsx: 'React TypeScript Component',
    py: 'Python Script',
    java: 'Java Source',
    cpp: 'C++ Source',
    c: 'C Source',
    cs: 'C# Source',
    php: 'PHP Script',
    rb: 'Ruby Script',
    go: 'Go Source',
    rs: 'Rust Source',
    swift: 'Swift Source',
    kt: 'Kotlin Source',
    html: 'HTML Document',
    css: 'Stylesheet',
    
    // Media
    jpg: 'JPEG Image',
    jpeg: 'JPEG Image',
    png: 'PNG Image',
    gif: 'GIF Image',
    svg: 'SVG Vector Image',
    mp4: 'MP4 Video',
    avi: 'AVI Video',
    mov: 'QuickTime Video',
    mp3: 'MP3 Audio',
    wav: 'WAV Audio',
    flac: 'FLAC Audio',
    
    // Archives
    zip: 'ZIP Archive',
    rar: 'RAR Archive',
    '7z': '7-Zip Archive',
    tar: 'TAR Archive',
    gz: 'Gzip Archive',
    
    // Data
    json: 'JSON Data',
    xml: 'XML Data',
    yaml: 'YAML Data',
    yml: 'YAML Data',
    sql: 'SQL Database',
    
    // System
    ini: 'Configuration File',
    cfg: 'Configuration File',
    conf: 'Configuration File',
    env: 'Environment File'
  }
  
  return descriptions[extension] || 'File'
}
