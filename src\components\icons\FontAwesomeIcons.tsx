/**
 * Centralized FontAwesome Icon System for ChatLo
 * 
 * This file contains ALL FontAwesome icons used throughout the application.
 * Instead of importing icons randomly in each component, all components
 * should import from this centralized system.
 * 
 * Benefits:
 * - No more missing icon errors
 * - Consistent icon usage across the app
 * - Easy to manage and update icons
 * - Better tree-shaking and bundle optimization
 * - Single source of truth for all icons
 */

import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import {
  // Navigation & UI
  faHome,
  faComment,
  faClockRotateLeft,
  faFolderTree,
  faUser,
  faGear,
  faBars,
  faTimes,
  faPlus,
  faMinus,
  faSearch,
  faFilter,
  faSort,
  faSortUp,
  faSortDown,
  faList,
  faTh,
  faGrip,
  faEllipsisVertical,
  faEllipsisHorizontal,
  faChevronLeft,
  faChevronRight,
  faChevronUp,
  faChevronDown,
  faArrowLeft,
  faArrowRight,
  faArrowUp,
  faArrowDown,
  faExpand,
  faCompress,
  faMaximize,
  faMinimize,
  
  // File & Document Icons
  faFile,
  faFileText,
  faFileCode,
  faFileImage,
  faFilePdf,
  faFileWord,
  faFileExcel,
  faFilePowerpoint,
  faFileArchive,
  faFileVideo,
  faFileAudio,
  faFileLines,
  faFolder,
  faFolderOpen,
  faFolderPlus,
  
  // Actions & Operations
  faEdit,
  faSave,
  faDownload,
  faUpload,
  faShare,
  faTrash,
  faCopy,
  faCut,
  faPaste,
  faUndo,
  faRedo,
  faPlay,
  faPause,
  faStop,
  faRefresh,
  faSync,
  faRotateLeft,
  faRotateRight,
  
  // Status & Feedback
  faCheck,
  faCheckCircle,
  faTimesCircle,
  faExclamationTriangle,
  faExclamationCircle,
  faInfoCircle,
  faQuestionCircle,
  faSpinner,
  faCircleNotch,
  
  // Media & Content
  faImage,
  faImages,
  faVideo,
  faMusic,
  faVolumeUp,
  faVolumeDown,
  faVolumeMute,
  faCamera,
  faMicrophone,
  
  // Communication
  faPaperPlane,
  faEnvelope,
  faPhone,
  faComments,
  faReply,
  faForward,
  
  // System & Settings
  faDesktop,
  faLaptop,
  faMobile,
  faTablet,
  faWifi,
  faSignal,
  faBattery,
  faPowerOff,
  faLock,
  faUnlock,
  faKey,
  faShield,
  
  // Data & Database
  faDatabase,
  faServer,
  faCloud,
  faCloudDownload,
  faCloudUpload,
  faHdd,
  
  // Development & Code
  faCode,
  faCodeBranch,
  faTerminal,
  faBug,
  faWrench,
  faTools,
  faCog,
  faCogs,
  
  // Text & Typography
  faFont,
  faBold,
  faItalic,
  faUnderline,
  faStrikethrough,
  faAlignLeft,
  faAlignCenter,
  faAlignRight,
  faAlignJustify,
  faIndent,
  faOutdent,
  faListUl,
  faListOl,
  
  // UI Elements
  faTable,
  faColumns,
  faGripVertical,
  faGripHorizontal,
  faBorderAll,
  faSquare,
  faCircle,
  faStar,
  faHeart,
  faBookmark,
  faTag,
  faTags,
  faPin,
  faPaperclip,
  faLink,
  faUnlink,
  faExternalLinkAlt,
  
  // Miscellaneous
  faCalendar,
  faClock,
  faStopwatch,
  faHistory,
  faMapMarker,
  faGlobe,
  faLanguage,
  faFlag,
  faBell,
  faBellSlash,
  faEye,
  faEyeSlash,
  faThumbsUp,
  faThumbsDown,
  faLightbulb,
  faRocket,
  faAward,
  faTrophy,
  faGift,
  faMagic,
  faRandom,
  faDice,
  faPuzzlePiece,
  faGamepad,
  faRobot,
  faUserRobot
} from '@fortawesome/free-solid-svg-icons'

// Export all icons as a centralized object for easy access
export const Icons = {
  // Navigation & UI
  home: faHome,
  comment: faComment,
  clockRotateLeft: faClockRotateLeft,
  folderTree: faFolderTree,
  user: faUser,
  gear: faGear,
  bars: faBars,
  times: faTimes,
  plus: faPlus,
  minus: faMinus,
  search: faSearch,
  filter: faFilter,
  sort: faSort,
  sortUp: faSortUp,
  sortDown: faSortDown,
  list: faList,
  th: faTh,
  grip: faGrip,
  ellipsisVertical: faEllipsisVertical,
  ellipsisHorizontal: faEllipsisHorizontal,
  chevronLeft: faChevronLeft,
  chevronRight: faChevronRight,
  chevronUp: faChevronUp,
  chevronDown: faChevronDown,
  arrowLeft: faArrowLeft,
  arrowRight: faArrowRight,
  arrowUp: faArrowUp,
  arrowDown: faArrowDown,
  expand: faExpand,
  compress: faCompress,
  maximize: faMaximize,
  minimize: faMinimize,
  
  // File & Document Icons
  file: faFile,
  fileText: faFileText,
  fileCode: faFileCode,
  fileImage: faFileImage,
  filePdf: faFilePdf,
  fileWord: faFileWord,
  fileExcel: faFileExcel,
  filePowerpoint: faFilePowerpoint,
  fileArchive: faFileArchive,
  fileVideo: faFileVideo,
  fileAudio: faFileAudio,
  fileLines: faFileLines,
  folder: faFolder,
  folderOpen: faFolderOpen,
  folderPlus: faFolderPlus,
  
  // Actions & Operations
  edit: faEdit,
  save: faSave,
  download: faDownload,
  upload: faUpload,
  share: faShare,
  trash: faTrash,
  copy: faCopy,
  cut: faCut,
  paste: faPaste,
  undo: faUndo,
  redo: faRedo,
  play: faPlay,
  pause: faPause,
  stop: faStop,
  refresh: faRefresh,
  sync: faSync,
  rotateLeft: faRotateLeft,
  rotateRight: faRotateRight,
  
  // Status & Feedback
  check: faCheck,
  checkCircle: faCheckCircle,
  timesIcon: faTimes,
  timesCircle: faTimesCircle,
  exclamationTriangle: faExclamationTriangle,
  exclamationCircle: faExclamationCircle,
  infoCircle: faInfoCircle,
  questionCircle: faQuestionCircle,
  spinner: faSpinner,
  circleNotch: faCircleNotch,
  
  // Media & Content
  image: faImage,
  images: faImages,
  video: faVideo,
  music: faMusic,
  volumeUp: faVolumeUp,
  volumeDown: faVolumeDown,
  volumeMute: faVolumeMute,
  camera: faCamera,
  microphone: faMicrophone,
  
  // Communication
  paperPlane: faPaperPlane,
  envelope: faEnvelope,
  phone: faPhone,
  comments: faComments,
  reply: faReply,
  forward: faForward,
  
  // System & Settings
  desktop: faDesktop,
  laptop: faLaptop,
  mobile: faMobile,
  tablet: faTablet,
  wifi: faWifi,
  signal: faSignal,
  battery: faBattery,
  powerOff: faPowerOff,
  lock: faLock,
  unlock: faUnlock,
  key: faKey,
  shield: faShield,
  
  // Data & Database
  database: faDatabase,
  server: faServer,
  cloud: faCloud,
  cloudDownload: faCloudDownload,
  cloudUpload: faCloudUpload,
  hdd: faHdd,
  
  // Development & Code
  code: faCode,
  codeBranch: faCodeBranch,
  terminal: faTerminal,
  bug: faBug,
  wrench: faWrench,
  tools: faTools,
  cog: faCog,
  cogs: faCogs,
  
  // Text & Typography
  font: faFont,
  bold: faBold,
  italic: faItalic,
  underline: faUnderline,
  strikethrough: faStrikethrough,
  alignLeft: faAlignLeft,
  alignCenter: faAlignCenter,
  alignRight: faAlignRight,
  alignJustify: faAlignJustify,
  indent: faIndent,
  outdent: faOutdent,
  listUl: faListUl,
  listOl: faListOl,
  
  // UI Elements
  table: faTable,
  columns: faColumns,
  gripVertical: faGripVertical,
  gripHorizontal: faGripHorizontal,
  borderAll: faBorderAll,
  square: faSquare,
  circle: faCircle,
  star: faStar,
  heart: faHeart,
  bookmark: faBookmark,
  tag: faTag,
  tags: faTags,
  pin: faPin,
  paperclip: faPaperclip,
  link: faLink,
  unlink: faUnlink,
  externalLinkAlt: faExternalLinkAlt,
  
  // Miscellaneous
  calendar: faCalendar,
  clock: faClock,
  stopwatch: faStopwatch,
  history: faHistory,
  mapMarker: faMapMarker,
  globe: faGlobe,
  language: faLanguage,
  flag: faFlag,
  bell: faBell,
  bellSlash: faBellSlash,
  eye: faEye,
  eyeSlash: faEyeSlash,
  thumbsUp: faThumbsUp,
  thumbsDown: faThumbsDown,
  lightbulb: faLightbulb,
  rocket: faRocket,
  award: faAward,
  trophy: faTrophy,
  gift: faGift,
  magic: faMagic,
  random: faRandom,
  dice: faDice,
  puzzlePiece: faPuzzlePiece,
  gamepad: faGamepad,
  robot: faRobot,
  userRobot: faUserRobot,
  ruler: faSquare,
  weight: faCircle
}

// Convenience component for easy icon usage
interface IconProps {
  name: keyof typeof Icons
  className?: string
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl'
  spin?: boolean
  pulse?: boolean
}

export const Icon: React.FC<IconProps> = ({ 
  name, 
  className = '', 
  size = 'md',
  spin = false,
  pulse = false 
}) => {
  const sizeClasses = {
    xs: 'w-3 h-3',
    sm: 'w-4 h-4', 
    md: 'w-5 h-5',
    lg: 'w-6 h-6',
    xl: 'w-8 h-8',
    '2xl': 'w-10 h-10'
  }

  return (
    <FontAwesomeIcon
      icon={Icons[name]}
      className={`${sizeClasses[size]} ${className}`}
      spin={spin}
      pulse={pulse}
    />
  )
}

// Export FontAwesome component for direct use when needed
export { FontAwesomeIcon }

// Export the Icons object as default for easy importing
export default Icons
